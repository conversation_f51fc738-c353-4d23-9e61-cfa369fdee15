#!/usr/bin/env python3
"""
性能测试脚本 - 测试 /api/summary_data API 的响应时间
"""

import requests
import time
import json
import statistics
from datetime import datetime, timedelta

# 测试配置
BASE_URL = "http://127.0.0.1:5000"
API_ENDPOINT = "/api/summary_data"
TEST_ITERATIONS = 5  # 测试次数

# 测试参数
test_params = {
    "start_date": "2025-07-26",
    "end_date": "2025-08-02"
}

def test_api_performance():
    """测试API性能"""
    print("=" * 60)
    print("API性能测试开始")
    print("=" * 60)
    print(f"测试URL: {BASE_URL}{API_ENDPOINT}")
    print(f"测试参数: {test_params}")
    print(f"测试次数: {TEST_ITERATIONS}")
    print("-" * 60)
    
    response_times = []
    success_count = 0
    
    for i in range(TEST_ITERATIONS):
        print(f"第 {i+1} 次测试...", end=" ")
        
        try:
            start_time = time.time()
            response = requests.get(
                f"{BASE_URL}{API_ENDPOINT}",
                params=test_params,
                timeout=30  # 30秒超时
            )
            end_time = time.time()
            
            response_time = (end_time - start_time) * 1000  # 转换为毫秒
            response_times.append(response_time)
            
            if response.status_code == 200:
                success_count += 1
                print(f"✓ {response_time:.2f}ms")
                
                # 解析响应数据
                try:
                    data = response.json()
                    if 'data' in data:
                        summary = data['data']
                        print(f"    订单数: {summary.get('total_orders', 0)}")
                        print(f"    总收入: ¥{summary.get('total_revenue', 0):.2f}")
                        print(f"    衣物件数: {summary.get('total_items', 0)}")
                except json.JSONDecodeError:
                    print("    响应数据解析失败")
            else:
                print(f"✗ HTTP {response.status_code}")
                
        except requests.exceptions.Timeout:
            print("✗ 请求超时 (>30s)")
        except requests.exceptions.RequestException as e:
            print(f"✗ 请求失败: {e}")
        
        # 测试间隔
        if i < TEST_ITERATIONS - 1:
            time.sleep(1)
    
    print("-" * 60)
    print("测试结果统计:")
    print(f"成功次数: {success_count}/{TEST_ITERATIONS}")
    
    if response_times:
        avg_time = statistics.mean(response_times)
        min_time = min(response_times)
        max_time = max(response_times)
        
        print(f"平均响应时间: {avg_time:.2f}ms")
        print(f"最快响应时间: {min_time:.2f}ms")
        print(f"最慢响应时间: {max_time:.2f}ms")
        
        if len(response_times) > 1:
            std_dev = statistics.stdev(response_times)
            print(f"标准差: {std_dev:.2f}ms")
        
        # 性能评估
        print("\n性能评估:")
        if avg_time < 1000:
            print("🟢 优秀 (< 1秒)")
        elif avg_time < 3000:
            print("🟡 良好 (1-3秒)")
        elif avg_time < 10000:
            print("🟠 一般 (3-10秒)")
        else:
            print("🔴 需要优化 (> 10秒)")
    
    print("=" * 60)

def test_with_different_date_ranges():
    """测试不同日期范围的性能"""
    print("\n测试不同日期范围的性能:")
    print("-" * 40)
    
    test_cases = [
        {"name": "7天", "days": 7},
        {"name": "30天", "days": 30},
        {"name": "90天", "days": 90}
    ]
    
    for case in test_cases:
        end_date = datetime.now()
        start_date = end_date - timedelta(days=case["days"])
        
        params = {
            "start_date": start_date.strftime("%Y-%m-%d"),
            "end_date": end_date.strftime("%Y-%m-%d")
        }
        
        print(f"{case['name']}数据: ", end="")
        
        try:
            start_time = time.time()
            response = requests.get(
                f"{BASE_URL}{API_ENDPOINT}",
                params=params,
                timeout=30
            )
            end_time = time.time()
            
            response_time = (end_time - start_time) * 1000
            
            if response.status_code == 200:
                data = response.json()
                if 'data' in data:
                    summary = data['data']
                    print(f"{response_time:.2f}ms (订单: {summary.get('total_orders', 0)})")
                else:
                    print(f"{response_time:.2f}ms (数据格式错误)")
            else:
                print(f"HTTP {response.status_code}")
                
        except Exception as e:
            print(f"失败: {e}")

if __name__ == "__main__":
    try:
        # 主要性能测试
        test_api_performance()
        
        # 不同日期范围测试
        test_with_different_date_ranges()
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")
