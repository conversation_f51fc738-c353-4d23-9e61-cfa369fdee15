-- =====================================================
-- 优化 /api/summary_data API 性能的数据库索引
-- =====================================================

-- 检查当前索引状态
SELECT '=== 检查当前索引状态 ===' AS info;

-- 检查Order表当前索引
SELECT 
    'Order表当前索引' AS table_info,
    INDEX_NAME,
    COLUMN_NAME,
    CARDINALITY,
    INDEX_TYPE
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'order'
ORDER BY INDEX_NAME;

-- 检查RechargeRecord表当前索引
SELECT 
    'RechargeRecord表当前索引' AS table_info,
    INDEX_NAME,
    COLUMN_NAME,
    CARDINALITY,
    INDEX_TYPE
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'recharge_record'
ORDER BY INDEX_NAME;

-- =====================================================
-- 创建针对 /api/summary_data 的优化索引
-- =====================================================
SELECT '=== 开始创建优化索引 ===' AS info;

-- Order表索引优化
SELECT '正在为Order表添加summary_data API优化索引...' AS status;

-- 1. 支付时间索引（最重要的筛选条件）
ALTER TABLE `order` ADD INDEX idx_payment_time (payment_time);

-- 2. 支付状态索引
ALTER TABLE `order` ADD INDEX idx_payment_status (payment_status);

-- 3. 复合索引：支付时间 + 支付状态 + 支付方式（覆盖主要查询条件）
ALTER TABLE `order` ADD INDEX idx_payment_composite (payment_time, payment_status, payment_method);

-- 4. 营业员索引（用于权限过滤和统计）
ALTER TABLE `order` ADD INDEX idx_operator (operator);

-- 5. 复合索引：营业员 + 支付时间（营业员筛选时的优化）
ALTER TABLE `order` ADD INDEX idx_operator_payment_time (operator, payment_time);

-- 6. 区域索引（区域管理员权限过滤）
ALTER TABLE `order` ADD INDEX idx_area (area);

SELECT 'Order表索引创建完成' AS status;

-- RechargeRecord表索引优化
SELECT '正在为RechargeRecord表添加索引...' AS status;

-- 1. 创建时间索引（充值记录时间筛选）
ALTER TABLE recharge_record ADD INDEX idx_created_at (created_at);

-- 2. 营业员索引（权限过滤）
ALTER TABLE recharge_record ADD INDEX idx_operator (operator);

-- 3. 复合索引：创建时间 + 营业员（主要查询模式）
ALTER TABLE recharge_record ADD INDEX idx_created_operator (created_at, operator);

-- 4. 支付方式索引（充值方式统计）
ALTER TABLE recharge_record ADD INDEX idx_payment_method (payment_method);

SELECT 'RechargeRecord表索引创建完成' AS status;

-- Clothing表索引优化（衣物统计相关）
SELECT '正在为Clothing表添加索引...' AS status;

-- 1. 订单ID索引（如果不存在）
-- ALTER TABLE clothing ADD INDEX idx_order_id (order_id); -- 通常已存在外键索引

-- 2. 客户ID索引（如果不存在）
-- ALTER TABLE clothing ADD INDEX idx_customer_id (customer_id); -- 通常已存在外键索引

SELECT 'Clothing表索引检查完成' AS status;

-- RefundRecord表索引优化（退款统计相关）
SELECT '正在为RefundRecord表添加索引...' AS status;

-- 1. 订单ID索引（如果不存在）
-- ALTER TABLE refund_record ADD INDEX idx_order_id (order_id); -- 通常已存在外键索引

-- 2. 创建时间索引
ALTER TABLE refund_record ADD INDEX idx_created_at (created_at);

SELECT 'RefundRecord表索引创建完成' AS status;

-- =====================================================
-- 验证索引创建结果
-- =====================================================
SELECT '=== 索引创建结果验证 ===' AS info;

-- 统计Order表的索引数量
SELECT 
    'Order表索引统计' AS table_info,
    COUNT(DISTINCT INDEX_NAME) as index_count,
    COUNT(*) as total_columns_indexed
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'order';

-- 统计RechargeRecord表的索引数量
SELECT 
    'RechargeRecord表索引统计' AS table_info,
    COUNT(DISTINCT INDEX_NAME) as index_count,
    COUNT(*) as total_columns_indexed
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'recharge_record';

-- 显示新创建的关键索引
SELECT 
    'Order表关键索引' AS table_info,
    INDEX_NAME,
    COLUMN_NAME,
    CARDINALITY
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'order'
AND INDEX_NAME IN ('idx_payment_time', 'idx_payment_composite', 'idx_operator_payment_time')
ORDER BY INDEX_NAME, SEQ_IN_INDEX;

SELECT 
    'RechargeRecord表关键索引' AS table_info,
    INDEX_NAME,
    COLUMN_NAME,
    CARDINALITY
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'recharge_record'
AND INDEX_NAME IN ('idx_created_at', 'idx_created_operator')
ORDER BY INDEX_NAME, SEQ_IN_INDEX;

-- =====================================================
-- 性能测试查询
-- =====================================================
SELECT '=== 性能测试查询示例 ===' AS info;

-- 测试主要查询的执行计划
EXPLAIN SELECT COUNT(*) 
FROM `order` 
WHERE payment_time BETWEEN '2025-07-26 00:00:00' AND '2025-08-02 23:59:59'
AND payment_status IN ('已付款', '部分退款')
AND payment_method != '余额';

-- 测试营业员筛选查询的执行计划
EXPLAIN SELECT COUNT(*) 
FROM `order` 
WHERE payment_time BETWEEN '2025-07-26 00:00:00' AND '2025-08-02 23:59:59'
AND payment_status IN ('已付款', '部分退款')
AND payment_method != '余额'
AND operator = '测试营业员';

-- 测试充值记录查询的执行计划
EXPLAIN SELECT COUNT(*) 
FROM recharge_record 
WHERE created_at BETWEEN '2025-07-26 00:00:00' AND '2025-08-02 23:59:59';

SELECT '=== 索引优化完成 ===' AS info;
SELECT '建议重启应用服务器以确保查询优化器使用新索引' AS recommendation;
